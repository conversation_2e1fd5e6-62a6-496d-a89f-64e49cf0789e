/**
 * Term Management Tests (T1-T18)
 * 
 * Agent 1: Implement test cases T1 through T18 from test_plan_part1.yaml
 * Focus: Academic term lifecycle, RBAC, state transitions, cascading effects
 * 
 * IMPORTANT: Read /spec.md first to understand the system!
 */

import {
  apiClient,
  createHeaders,
  createTermPlanning,
  createTermEnrollmentOpen,
  createTermEnrollmentClosed,
  createCourseOpen,
  createCourseWithWaitlist,
  TEST_USERS,
  handleApiResponse,
  validateErrorEnvelope,
  ApiErrorId,
  TermState,
  EnrollmentState,
  UserRole,
  assertAcademicTerm
} from './helpers';

describe('Term Management Tests', () => {
  describe('T1: Create Term - Success', () => {
    it('should allow Registrar to create a new academic term successfully', async () => {
      // Registrar creates a new academic term
      const headers = createHeaders(TEST_USERS.REGISTRAR.id, TEST_USERS.REGISTRAR.role);
      const response = await apiClient.createTerm(
        { name: 'Spring 2026' },
        headers
      );

      // Verify 201 Created status
      const term = handleApiResponse(response, 201);

      // Verify response data
      expect(term.state).toBe(TermState.PLANNING);
      expect(term.name).toBe('Spring 2026');

      // Validate full term structure
      assertAcademicTerm(term);
    });
  });

  describe('T2: Create Term - Unauthorized Role', () => {
    it('should reject term creation by non-Registrar users', async () => {
      // Student attempts to create a term
      const headers = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      const response = await apiClient.createTerm(
        { name: 'Hack Term' },
        headers
      );

      // Verify 403 Forbidden with specific error
      validateErrorEnvelope(response, {
        expectedErrorId: ApiErrorId.ERR_UNAUTHORIZED_ROLE
      });
    });
  });

  describe('T3: Create Term - Missing Name Field', () => {
    it('should reject term creation without required name field', async () => {
      // Registrar attempts to create term without name
      const headers = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const response = await apiClient.createTerm(
        {} as any, // Intentionally omitting name
        headers
      );

      // Verify 400 Bad Request with specific error
      validateErrorEnvelope(response, {
        expectedErrorId: ApiErrorId.ERR_MISSING_REQUIRED_FIELD
      });
    });
  });

  describe('T4: Create Term - Duplicate Term Name', () => {
    it('should reject creating a term with a duplicate name', async () => {
      // First create a term with name "Fall Test Term"
      await createTermPlanning('Fall Test Term');

      // Attempt to create another term with same name
      const headers = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const response = await apiClient.createTerm(
        { name: 'Fall Test Term' },
        headers
      );

      // Verify 409 Conflict with specific error
      validateErrorEnvelope(response, {
        expectedErrorId: ApiErrorId.ERR_TERM_NAME_NOT_UNIQUE
      });
    });
  });
  
  describe('T5: Open Term - Success', () => {
    it('should allow Registrar to open enrollment for a term', async () => {
      // Setup: Create term in PLANNING state
      const { term: planningTerm } = await createTermPlanning();

      // Registrar opens enrollment
      const headers = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const response = await apiClient.openTermRegistration(
        planningTerm.id,
        { revision: planningTerm.revision },
        headers
      );

      // Verify 200 OK status
      const term = handleApiResponse(response, 200);

      // Verify state transition
      expect(term.state).toBe(TermState.ENROLLMENT_OPEN);
      assertAcademicTerm(term);
    });
  });

  describe('T6: Open Term - Wrong Role', () => {
    it('should reject opening term by non-Registrar', async () => {
      // Setup: Create term in PLANNING state
      const { term: planningTerm } = await createTermPlanning();

      // Professor attempts to open term
      const headers = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);
      const response = await apiClient.openTermRegistration(
        planningTerm.id,
        { revision: planningTerm.revision },
        headers
      );

      // Verify 403 Forbidden with specific error
      validateErrorEnvelope(response, {
        expectedErrorId: ApiErrorId.ERR_UNAUTHORIZED_ROLE
      });
    });
  });

  describe('T7: Open Term - Term Wrong State', () => {
    it('should reject opening term that is not in PLANNING state', async () => {
      // Setup: Create term in ENROLLMENT_OPEN state
      const { term: openTerm } = await createTermEnrollmentOpen();

      // Attempt to open already open term
      const headers = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const response = await apiClient.openTermRegistration(
        openTerm.id,
        { revision: openTerm.revision },
        headers
      );

      // Verify 409 Conflict with specific error
      validateErrorEnvelope(response, {
        expectedErrorId: ApiErrorId.ERR_TERM_NOT_ACTIVE
      });
    });
  });

  describe('T8: Open Term - Revision Conflict', () => {
    it('should reject opening term with stale revision number', async () => {
      // Setup: Create term in PLANNING state
      const { term: planningTerm } = await createTermPlanning();

      // Attempt to open with outdated revision
      const headers = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const response = await apiClient.openTermRegistration(
        planningTerm.id,
        { revision: planningTerm.revision - 1 }, // Stale revision
        headers
      );

      // Verify 409 Conflict with specific error
      validateErrorEnvelope(response, {
        expectedErrorId: ApiErrorId.ERR_REV_CONFLICT
      });
    });
  });

  describe('T8A: Open Term - Missing Revision Field', () => {
    it('should reject opening term without required revision', async () => {
      // Setup: Create term in PLANNING state
      const { term } = await createTermPlanning();

      // Attempt to open without revision
      const headers = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const response = await apiClient.openTermRegistration(
        term.id,
        {} as any,
        headers
      );

      // Verify 400 Bad Request with specific error
      validateErrorEnvelope(response, {
        expectedErrorId: ApiErrorId.ERR_MISSING_REQUIRED_FIELD
      });
    });
  });

  describe('T9: Close Term - Success', () => {
    it('should allow Registrar to close enrollment for a term', async () => {
      // Setup: Create term in ENROLLMENT_OPEN state
      const { term: openTerm } = await createTermEnrollmentOpen();

      // Registrar closes enrollment
      const headers = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const response = await apiClient.closeTermRegistration(
        openTerm.id,
        { revision: openTerm.revision },
        headers
      );

      // Verify 200 OK status
      const term = handleApiResponse(response, 200);

      // Verify state transition
      expect(term.state).toBe(TermState.ENROLLMENT_CLOSED);
      assertAcademicTerm(term);
    });
  });

  describe('T10: Close Term - Unauthorized Role', () => {
    it('should reject closing term by non-Registrar', async () => {
      // Setup: Create term in ENROLLMENT_OPEN state
      const { term: openTerm } = await createTermEnrollmentOpen();

      // Student attempts to close term
      const headers = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      const response = await apiClient.closeTermRegistration(
        openTerm.id,
        { revision: openTerm.revision },
        headers
      );

      // Verify 403 Forbidden with specific error
      validateErrorEnvelope(response, {
        expectedErrorId: ApiErrorId.ERR_UNAUTHORIZED_ROLE
      });
    });
  });

  describe('T11: Close Term - Wrong State', () => {
    it('should reject closing term that is not open for enrollment', async () => {
      // Setup: Create term in PLANNING state
      const { term: planningTerm } = await createTermPlanning();

      // Attempt to close term that hasn't been opened
      const headers = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const response = await apiClient.closeTermRegistration(
        planningTerm.id,
        { revision: planningTerm.revision },
        headers
      );

      // Verify 409 Conflict with specific error
      validateErrorEnvelope(response, {
        expectedErrorId: ApiErrorId.ERR_TERM_NOT_ACTIVE
      });
    });
  });

  describe('T12: Close Term - Revision Conflict', () => {
    it('should reject closing term with out-of-date revision number', async () => {
      // Setup: Create term in ENROLLMENT_OPEN state
      const { term: openTerm } = await createTermEnrollmentOpen();

      // Attempt to close with outdated revision
      const headers = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const response = await apiClient.closeTermRegistration(
        openTerm.id,
        { revision: openTerm.revision - 1 }, // Stale revision
        headers
      );

      // Verify 409 Conflict with specific error
      validateErrorEnvelope(response, {
        expectedErrorId: ApiErrorId.ERR_REV_CONFLICT
      });
    });
  });

  describe('T12A: Close Term - Missing Revision Field', () => {
    it('should reject closing term without required revision', async () => {
      // Setup: Create term in ENROLLMENT_OPEN state
      const { term } = await createTermEnrollmentOpen();

      // Attempt to close without revision
      const headers = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const response = await apiClient.closeTermRegistration(
        term.id,
        {} as any,
        headers
      );

      // Verify 400 Bad Request with specific error
      validateErrorEnvelope(response, {
        expectedErrorId: ApiErrorId.ERR_MISSING_REQUIRED_FIELD
      });
    });
  });

  describe('T13: Close Term - Cascade Course States', () => {
    it('should automatically transition all OPEN courses to IN_PROGRESS when closing term', async () => {
      // Setup: Create term with an open course
      const { term: openTerm, course: openCourse } = await createCourseOpen();

      // Verify course is initially OPEN
      expect(openCourse.state).toBe('OPEN');

      // Registrar closes enrollment
      const headers = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const closeResponse = await apiClient.closeTermRegistration(
        openTerm.id,
        { revision: openTerm.revision },
        headers
      );

      // Verify term closed
      const term = handleApiResponse(closeResponse, 200);
      expect(term.state).toBe(TermState.ENROLLMENT_CLOSED);

      // Verify course state changed to IN_PROGRESS
      const courseResponse = await apiClient.getCourse(
        openTerm.id,
        openCourse.id,
        headers
      );
      const updatedCourse = handleApiResponse(courseResponse, 200);
      expect(updatedCourse.state).toBe('IN_PROGRESS');
    });
  });

  describe('T14: Conclude Term - Success', () => {
    it('should allow Registrar to conclude a term', async () => {
      // Setup: Create term in ENROLLMENT_CLOSED state
      const { term: closedTerm } = await createTermEnrollmentClosed();

      // Registrar concludes term
      const headers = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const response = await apiClient.concludeTerm(
        closedTerm.id,
        { revision: closedTerm.revision },
        headers
      );

      // Verify 200 OK status
      const term = handleApiResponse(response, 200);

      // Verify state transition
      expect(term.state).toBe(TermState.CONCLUDED);
      assertAcademicTerm(term);
    });
  });

  describe('T15: Conclude Term - Unauthorized Role', () => {
    it('should reject concluding term by non-Registrar', async () => {
      // Setup: Create term in ENROLLMENT_CLOSED state
      const { term: closedTerm } = await createTermEnrollmentClosed();

      // Professor attempts to conclude term
      const headers = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);
      const response = await apiClient.concludeTerm(
        closedTerm.id,
        { revision: closedTerm.revision },
        headers
      );

      // Verify 403 Forbidden with specific error
      validateErrorEnvelope(response, {
        expectedErrorId: ApiErrorId.ERR_UNAUTHORIZED_ROLE
      });
    });
  });

  describe('T16: Conclude Term - Wrong State', () => {
    it('should reject concluding term that is still open for enrollment', async () => {
      // Setup: Create term in ENROLLMENT_OPEN state
      const { term: openTerm } = await createTermEnrollmentOpen();

      // Attempt to conclude term without closing registration first
      const headers = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const response = await apiClient.concludeTerm(
        openTerm.id,
        { revision: openTerm.revision },
        headers
      );

      // Verify 409 Conflict with specific error
      validateErrorEnvelope(response, {
        expectedErrorId: ApiErrorId.ERR_TERM_NOT_ACTIVE
      });
    });
  });

  describe('T17: Conclude Term - Revision Conflict', () => {
    it('should reject concluding term with bad revision', async () => {
      // Setup: Create term in ENROLLMENT_CLOSED state
      const { term: closedTerm } = await createTermEnrollmentClosed();

      // Attempt to conclude with outdated revision
      const headers = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const response = await apiClient.concludeTerm(
        closedTerm.id,
        { revision: closedTerm.revision - 1 }, // Stale revision
        headers
      );

      // Verify 409 Conflict with specific error
      validateErrorEnvelope(response, {
        expectedErrorId: ApiErrorId.ERR_REV_CONFLICT
      });
    });
  });

  describe('T17A: Conclude Term - Missing Revision Field', () => {
    it('should reject concluding term without required revision', async () => {
      // Setup: Create term in ENROLLMENT_CLOSED state
      const { term } = await createTermEnrollmentClosed();

      // Attempt to conclude without revision
      const headers = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const response = await apiClient.concludeTerm(
        term.id,
        {} as any,
        headers
      );

      // Verify 400 Bad Request with specific error
      validateErrorEnvelope(response, {
        expectedErrorId: ApiErrorId.ERR_MISSING_REQUIRED_FIELD
      });
    });
  });

  describe('T18: Conclude Term - Cascade Course & Enrollment States', () => {
    it('should complete all in-progress courses and finalize enrollments when concluding term', async () => {
      // Setup: Create a complex scenario with courses and enrollments
      // First create an open term with a course
      const { term: openTerm, course: openCourse } = await createCourseOpen();
      
      // Enroll a student
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      const enrollResponse = await apiClient.createEnrollment(
        openTerm.id,
        openCourse.id,
        {},
        studentHeaders
      );
      const enrollment = handleApiResponse(enrollResponse, 201);

      // Close term registration (transitions course to IN_PROGRESS)
      const regHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const closeResponse = await apiClient.closeTermRegistration(
        openTerm.id,
        { revision: openTerm.revision },
        regHeaders
      );
      const closedTerm = handleApiResponse(closeResponse, 200);

      // Verify course is now IN_PROGRESS
      const inProgressCourseResponse = await apiClient.getCourse(
        openTerm.id,
        openCourse.id,
        regHeaders
      );
      const inProgressCourse = handleApiResponse(inProgressCourseResponse, 200);
      expect(inProgressCourse.state).toBe('IN_PROGRESS');

      // Now conclude the term
      const concludeResponse = await apiClient.concludeTerm(
        closedTerm.id,
        { revision: closedTerm.revision },
        regHeaders
      );
      const concludedTerm = handleApiResponse(concludeResponse, 200);
      expect(concludedTerm.state).toBe(TermState.CONCLUDED);

      // Verify course transitioned to COMPLETED
      const completedCourseResponse = await apiClient.getCourse(
        openTerm.id,
        openCourse.id,
        regHeaders
      );
      const completedCourse = handleApiResponse(completedCourseResponse, 200);
      expect(completedCourse.state).toBe('COMPLETED');

      // Verify enrolled student's enrollment is now COMPLETED
      const completedEnrollmentResponse = await apiClient.getEnrollment(
        openTerm.id,
        openCourse.id,
        enrollment.id,
        regHeaders
      );
      const completedEnrollment = handleApiResponse(completedEnrollmentResponse, 200);
      expect(completedEnrollment.state).toBe('COMPLETED');
    });
  });

  describe('T18b: Conclude Term - Waitlisted Enrollments Dropped', () => {
    it('should drop waitlisted enrollments when term is concluded', async () => {
      // Setup: course at full capacity with a waitlisted student
      const { term: openTerm, course, enrollments } = await createCourseWithWaitlist();

      // Close registration (course transitions to IN_PROGRESS)
      const regHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const closeResponse = await apiClient.closeTermRegistration(
        openTerm.id,
        { revision: openTerm.revision },
        regHeaders
      );
      const closedTerm = handleApiResponse(closeResponse, 200);

      // Conclude the term
      const concludeResponse = await apiClient.concludeTerm(
        closedTerm.id,
        { revision: closedTerm.revision },
        regHeaders
      );
      const concludedTerm = handleApiResponse(concludeResponse, 200);
      expect(concludedTerm.state).toBe(TermState.CONCLUDED);

      // Verify waitlisted enrollment is now DROPPED
      const waitlistedResp = await apiClient.getEnrollment(
        openTerm.id,
        course.id,
        enrollments.studentC.id,
        regHeaders
      );
      const waitlistedEnrollment = handleApiResponse(waitlistedResp, 200);
      expect(waitlistedEnrollment.state).toBe(EnrollmentState.DROPPED);

      // Previously enrolled student should be COMPLETED
      const enrolledResp = await apiClient.getEnrollment(
        openTerm.id,
        course.id,
        enrollments.studentA.id,
        regHeaders
      );
      const enrolledEnrollment = handleApiResponse(enrolledResp, 200);
      expect(enrolledEnrollment.state).toBe(EnrollmentState.COMPLETED);
    });
  });
});