const express = require('express');
const { v4: uuidv4 } = require('uuid');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.json());

// Constants
const COST_PER_CREDIT = 10000; // cents
const MAX_CREDITS_PER_TERM = 18;
const MAX_COURSES_PER_PROF = 5;
const MAX_DROP_COUNT_PER_TERM = 3;
const DROP_PENALTY_FEE = 5000; // cents
const FULL_TIME_CREDITS_THRESHOLD = 12;

// In-memory storage
const storage = {
  terms: new Map(),
  courses: new Map(),
  enrollments: new Map(),
  courseSeatLedgers: new Map(),
  studentTuitionLedgers: new Map(),
  users: new Map() // For validation purposes
};

// Error codes
const ERROR_CODES = {
  ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER: 'ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER',
  ERR_UNAUTHORIZED_ROLE: 'ERR_UNAUTHORIZED_ROLE',
  ERR_NOT_INSTRUCTOR: 'ERR_NOT_INSTRUCTOR',
  ERR_PERMISSION_DENIED: 'ERR_PERMISSION_DENIED',
  ERR_TERM_NOT_FOUND: 'ERR_TERM_NOT_FOUND',
  ERR_COURSE_NOT_FOUND: 'ERR_COURSE_NOT_FOUND',
  ERR_ENROLLMENT_NOT_FOUND: 'ERR_ENROLLMENT_NOT_FOUND',
  ERR_STUDENT_NOT_FOUND: 'ERR_STUDENT_NOT_FOUND',
  ERR_TERM_CLOSED: 'ERR_TERM_CLOSED',
  ERR_INVALID_ID_FORMAT: 'ERR_INVALID_ID_FORMAT',
  ERR_INVALID_COURSE_CODE: 'ERR_INVALID_COURSE_CODE',
  ERR_COURSE_CODE_NOT_UNIQUE: 'ERR_COURSE_CODE_NOT_UNIQUE',
  ERR_INVALID_FIELD_LENGTH: 'ERR_INVALID_FIELD_LENGTH',
  ERR_INVALID_CREDITS: 'ERR_INVALID_CREDITS',
  ERR_INVALID_CAPACITY: 'ERR_INVALID_CAPACITY',
  ERR_INVALID_ENUM_VALUE: 'ERR_INVALID_ENUM_VALUE',
  ERR_MISSING_REQUIRED_FIELD: 'ERR_MISSING_REQUIRED_FIELD',
  ERR_CONDITIONAL_FIELD_REQUIRED: 'ERR_CONDITIONAL_FIELD_REQUIRED',
  ERR_FIELD_CONFLICT: 'ERR_FIELD_CONFLICT',
  ERR_UNKNOWN_FIELD: 'ERR_UNKNOWN_FIELD',
  ERR_COURSE_WRONG_STATE: 'ERR_COURSE_WRONG_STATE',
  ERR_ENROLLMENT_WRONG_STATE: 'ERR_ENROLLMENT_WRONG_STATE',
  ERR_TERM_NOT_ACTIVE: 'ERR_TERM_NOT_ACTIVE',
  ERR_REGISTRATION_CLOSED: 'ERR_REGISTRATION_CLOSED',
  ERR_COURSE_FULL: 'ERR_COURSE_FULL',
  ERR_CAPACITY_EXCEEDED: 'ERR_CAPACITY_EXCEEDED',
  ERR_ALREADY_ENROLLED: 'ERR_ALREADY_ENROLLED',
  ERR_NOT_ENROLLED: 'ERR_NOT_ENROLLED',
  ERR_MAX_COURSES_REACHED: 'ERR_MAX_COURSES_REACHED',
  ERR_CREDIT_LIMIT_EXCEEDED: 'ERR_CREDIT_LIMIT_EXCEEDED',
  ERR_TOO_MANY_DROPS: 'ERR_TOO_MANY_DROPS',
  ERR_ILLEGAL_COURSE_STATE_TRANSITION: 'ERR_ILLEGAL_COURSE_STATE_TRANSITION',
  ERR_ILLEGAL_ENROLLMENT_STATE: 'ERR_ILLEGAL_ENROLLMENT_STATE',
  ERR_FORBIDDEN: 'ERR_FORBIDDEN',
  ERR_TERM_NAME_NOT_UNIQUE: 'ERR_TERM_NAME_NOT_UNIQUE',
  ERR_INVALID_INSTRUCTOR: 'ERR_INVALID_INSTRUCTOR',
  ERR_REV_CONFLICT: 'ERR_REV_CONFLICT',
  ERR_INSUFFICIENT_FUNDS: 'ERR_INSUFFICIENT_FUNDS',
  ERR_OVERPAY_NOT_ALLOWED: 'ERR_OVERPAY_NOT_ALLOWED',
  ERR_INVALID_PAYMENT_AMOUNT: 'ERR_INVALID_PAYMENT_AMOUNT',
  ERR_LEDGER_INVALID_OP: 'ERR_LEDGER_INVALID_OP',
  ERR_LEDGER_MISSING: 'ERR_LEDGER_MISSING'
};

// Utility functions
function generateRequestId() {
  return 'req_' + Math.random().toString(36).substr(2, 15).toUpperCase();
}

function createSuccessResponse(data, responseType = 'object') {
  return {
    meta: {
      api_request_id: generateRequestId(),
      api_request_timestamp: new Date().toISOString()
    },
    response_type: responseType,
    data: data
  };
}

function createErrorResponse(errorId, message) {
  return {
    meta: {
      api_request_id: generateRequestId(),
      api_request_timestamp: new Date().toISOString()
    },
    response_type: 'error',
    data: {
      error_id: errorId,
      message: message
    }
  };
}

function isValidUUID(id) {
  const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
  return uuidRegex.test(id);
}

function validateCourseCode(code) {
  const codeRegex = /^[A-Z]{2,4}[0-9]{3}$/;
  return codeRegex.test(code);
}

// Authentication middleware
function authenticateUser(req, res, next) {
  const userId = req.headers['x-user-id'];
  const userRole = req.headers['x-user-role'];

  if (!userId || !userRole) {
    return res.status(400).json(createErrorResponse(
      ERROR_CODES.ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER,
      'Missing or invalid user context headers'
    ));
  }

  if (!isValidUUID(userId)) {
    return res.status(400).json(createErrorResponse(
      ERROR_CODES.ERR_INVALID_ID_FORMAT,
      'Invalid user ID format'
    ));
  }

  if (!['STUDENT', 'PROFESSOR', 'REGISTRAR'].includes(userRole)) {
    return res.status(400).json(createErrorResponse(
      ERROR_CODES.ERR_INVALID_ENUM_VALUE,
      'Invalid user role'
    ));
  }

  req.auth = { id: userId, role: userRole };

  // Register the user in in-memory storage if not already present. This enables
  // later look-ups (e.g., registrar enrolling a student) to verify that the
  // referenced user actually exists and has the expected role.
  if (!storage.users.has(userId)) {
    storage.users.set(userId, { id: userId, role: userRole });
  }

  next();
}

app.use(authenticateUser);

// Ledger operations
function initCourseSeatLedger(termId, courseId, capacity) {
  const key = `${termId}-${courseId}`;
  storage.courseSeatLedgers.set(key, {
    term_id: termId,
    course_id: courseId,
    seats_available: capacity
  });
}

function initStudentTuitionLedger(termId, studentId) {
  const key = `${termId}-${studentId}`;
  if (!storage.studentTuitionLedgers.has(key)) {
    storage.studentTuitionLedgers.set(key, {
      term_id: termId,
      student_id: studentId,
      balance_cents: 0
    });
  }
}

function debitCourseSeatLedger(termId, courseId, amount = 1) {
  const key = `${termId}-${courseId}`;
  const ledger = storage.courseSeatLedgers.get(key);
  if (!ledger) throw new Error('Ledger not found');
  if (ledger.seats_available < amount) throw new Error('Insufficient seats');
  ledger.seats_available -= amount;
}

function creditCourseSeatLedger(termId, courseId, amount = 1) {
  const key = `${termId}-${courseId}`;
  const ledger = storage.courseSeatLedgers.get(key);
  if (!ledger) throw new Error('Ledger not found');
  const course = storage.courses.get(courseId);
  if (ledger.seats_available + amount > course.capacity) throw new Error('Capacity exceeded');
  ledger.seats_available += amount;
}

function creditStudentTuitionLedger(termId, studentId, amount) {
  initStudentTuitionLedger(termId, studentId);
  const key = `${termId}-${studentId}`;
  const ledger = storage.studentTuitionLedgers.get(key);
  ledger.balance_cents += amount;
}

function debitStudentTuitionLedger(termId, studentId, amount) {
  const key = `${termId}-${studentId}`;
  const ledger = storage.studentTuitionLedgers.get(key);
  if (!ledger) throw new Error('Ledger not found');
  if (ledger.balance_cents < amount) throw new Error('Insufficient balance');
  ledger.balance_cents -= amount;
}

// Add a safe debit helper specifically for refunds that should not error if the
// refund amount exceeds the current balance – it should simply zero-out the
// ledger (the spec requires that the balance never go negative, but students
// are entitled to receive up to what they have actually paid).
function safeDebitStudentTuitionLedger(termId, studentId, amount) {
  initStudentTuitionLedger(termId, studentId);
  const key = `${termId}-${studentId}`;
  const ledger = storage.studentTuitionLedgers.get(key);
  const debitAmount = Math.min(amount, ledger.balance_cents);
  if (debitAmount > 0) {
    ledger.balance_cents -= debitAmount;
  }
}

// Derived field calculations
function getEnrolledCount(courseId) {
  return Array.from(storage.enrollments.values())
    .filter(e => e.course_id === courseId && e.state === 'ENROLLED').length;
}

function getWaitlistCount(courseId) {
  return Array.from(storage.enrollments.values())
    .filter(e => e.course_id === courseId && e.state === 'WAITLISTED').length;
}

function getAvailableSeats(termId, courseId) {
  const key = `${termId}-${courseId}`;
  const ledger = storage.courseSeatLedgers.get(key);
  return ledger ? ledger.seats_available : 0;
}

function getStudentDropCount(termId, studentId) {
  return Array.from(storage.enrollments.values())
    .filter(
      e =>
        e.term_id === termId &&
        e.student_id === studentId &&
        e.state === 'DROPPED' &&
        e.dropped_by_student === true
    ).length;
}

function getStudentEnrolledCredits(termId, studentId) {
  const enrollments = Array.from(storage.enrollments.values())
    .filter(e => e.term_id === termId && e.student_id === studentId && e.state === 'ENROLLED');
  
  return enrollments.reduce((total, enrollment) => {
    const course = storage.courses.get(enrollment.course_id);
    return total + (course ? course.credits : 0);
  }, 0);
}

function getProfessorCourseCount(termId, professorId) {
  return Array.from(storage.courses.values())
    .filter(c => c.term_id === termId && c.professor_id === professorId && !['CANCELLED','COMPLETED'].includes(c.state))
    .length;
}

// Event handling for waitlist promotion
function triggerSeatVacated(courseId) {
  const course = storage.courses.get(courseId);
  if (!course) return;

  const waitlistEnrollments = Array.from(storage.enrollments.values())
    .filter(e => e.course_id === courseId && e.state === 'WAITLISTED')
    .sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

  const seatLedger = storage.courseSeatLedgers.get(`${course.term_id}-${courseId}`);
  
  if (!seatLedger || seatLedger.seats_available <= 0) return;
  
  // Iterate through waitlist to find eligible students
  for (const enrollmentToPromote of waitlistEnrollments) {
    // Check if we still have seats available
    if (seatLedger.seats_available <= 0) break;
    
    // Check if promoting this student would exceed credit limit
    const studentCurrentCredits = getStudentEnrolledCredits(course.term_id, enrollmentToPromote.student_id);
    if (studentCurrentCredits + course.credits > MAX_CREDITS_PER_TERM) {
      // Skip this student and continue to the next one
      continue;
    }
    
    // Promote from waitlist to enrolled
    enrollmentToPromote.state = 'ENROLLED';
    enrollmentToPromote.revision += 1;
    
    // Update ledgers
    debitCourseSeatLedger(course.term_id, courseId, 1);
    creditStudentTuitionLedger(course.term_id, enrollmentToPromote.student_id, course.credits * COST_PER_CREDIT);
  }
}

// Validation helpers
function validateRequiredFields(body, fields) {
  for (const field of fields) {
    if (body[field] === undefined || body[field] === null || body[field] === '') {
      throw {
        status: 400,
        errorId: ERROR_CODES.ERR_MISSING_REQUIRED_FIELD,
        message: `Required field '${field}' is missing`
      };
    }
  }
}

function validateUnknownFields(body, allowedFields) {
  const unknownFields = Object.keys(body).filter(field => !allowedFields.includes(field));
  if (unknownFields.length > 0) {
    throw {
      status: 400,
      errorId: ERROR_CODES.ERR_UNKNOWN_FIELD,
      message: `Unknown field(s): ${unknownFields.join(', ')}`
    };
  }
}

// Term Management Endpoints

// POST /terms - Create a new academic term
app.post('/terms', (req, res) => {
  try {
    if (req.auth.role !== 'REGISTRAR') {
      return res.status(403).json(createErrorResponse(
        ERROR_CODES.ERR_UNAUTHORIZED_ROLE,
        'Only registrars can create terms'
      ));
    }

    validateRequiredFields(req.body, ['name']);
    validateUnknownFields(req.body, ['name']);

    if (req.body.name.length === 0 || req.body.name.length > 100) {
      return res.status(400).json(createErrorResponse(
        ERROR_CODES.ERR_INVALID_FIELD_LENGTH,
        'Term name must be between 1 and 100 characters'
      ));
    }

    // Check term name uniqueness
    const existingTerm = Array.from(storage.terms.values())
      .find(term => term.name === req.body.name);
    if (existingTerm) {
      return res.status(409).json(createErrorResponse(
        ERROR_CODES.ERR_TERM_NAME_NOT_UNIQUE,
        'Term name must be unique'
      ));
    }

    const termId = uuidv4();
    const term = {
      id: termId,
      name: req.body.name,
      state: 'PLANNING',
      created_by: req.auth.id,
      created_at: new Date().toISOString(),
      revision: 0
    };

    storage.terms.set(termId, term);

    res.status(201).json(createSuccessResponse(term));
  } catch (error) {
    if (error.status) {
      return res.status(error.status).json(createErrorResponse(error.errorId, error.message));
    }
    res.status(500).json(createErrorResponse('ERR_INTERNAL_SERVER_ERROR', 'Internal server error'));
  }
});

// GET /terms/{termId} - Get academic term details
app.get('/terms/:termId', (req, res) => {
  try {
    if (!isValidUUID(req.params.termId)) {
      return res.status(400).json(createErrorResponse(
        ERROR_CODES.ERR_INVALID_ID_FORMAT,
        'Invalid term ID format'
      ));
    }

    const term = storage.terms.get(req.params.termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        ERROR_CODES.ERR_TERM_NOT_FOUND,
        'Term not found'
      ));
    }

    res.json(createSuccessResponse(term));
  } catch (error) {
    res.status(500).json(createErrorResponse('ERR_INTERNAL_SERVER_ERROR', 'Internal server error'));
  }
});

// PATCH /terms/{termId}:open-registration - Open registration for a term
app.patch('/terms/:termId([0-9a-fA-F\-]{36})\\:open-registration', (req, res) => {
  try {
    if (req.auth.role !== 'REGISTRAR') {
      return res.status(403).json(createErrorResponse(
        ERROR_CODES.ERR_UNAUTHORIZED_ROLE,
        'Only registrars can open registration'
      ));
    }

    if (!isValidUUID(req.params.termId)) {
      return res.status(400).json(createErrorResponse(
        ERROR_CODES.ERR_INVALID_ID_FORMAT,
        'Invalid term ID format'
      ));
    }

    const term = storage.terms.get(req.params.termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        ERROR_CODES.ERR_TERM_NOT_FOUND,
        'Term not found'
      ));
    }

    if (req.body.revision === undefined) {
      return res.status(400).json(createErrorResponse(
        ERROR_CODES.ERR_MISSING_REQUIRED_FIELD,
        "Required field 'revision' is missing"
      ));
    }

    if (req.body.revision !== term.revision) {
      return res.status(409).json(createErrorResponse(
        ERROR_CODES.ERR_REV_CONFLICT,
        'Term revision conflict'
      ));
    }

    if (term.state !== 'PLANNING') {
      return res.status(409).json(createErrorResponse(
        ERROR_CODES.ERR_TERM_NOT_ACTIVE,
        'Term must be in PLANNING state to open registration'
      ));
    }

    term.state = 'ENROLLMENT_OPEN';
    term.revision += 1;

    res.json(createSuccessResponse(term));
  } catch (error) {
    res.status(500).json(createErrorResponse('ERR_INTERNAL_SERVER_ERROR', 'Internal server error'));
  }
});

// PATCH /terms/{termId}:close-registration - Close registration for a term
app.patch('/terms/:termId([0-9a-fA-F\-]{36})\\:close-registration', (req, res) => {
  try {
    if (req.auth.role !== 'REGISTRAR') {
      return res.status(403).json(createErrorResponse(
        ERROR_CODES.ERR_UNAUTHORIZED_ROLE,
        'Only registrars can close registration'
      ));
    }

    if (!isValidUUID(req.params.termId)) {
      return res.status(400).json(createErrorResponse(
        ERROR_CODES.ERR_INVALID_ID_FORMAT,
        'Invalid term ID format'
      ));
    }

    const term = storage.terms.get(req.params.termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        ERROR_CODES.ERR_TERM_NOT_FOUND,
        'Term not found'
      ));
    }

    if (req.body.revision === undefined) {
      return res.status(400).json(createErrorResponse(
        ERROR_CODES.ERR_MISSING_REQUIRED_FIELD,
        "Required field 'revision' is missing"
      ));
    }

    if (req.body.revision !== term.revision) {
      return res.status(409).json(createErrorResponse(
        ERROR_CODES.ERR_REV_CONFLICT,
        'Term revision conflict'
      ));
    }

    if (term.state !== 'ENROLLMENT_OPEN') {
      return res.status(409).json(createErrorResponse(
        ERROR_CODES.ERR_TERM_NOT_ACTIVE,
        'Term must be in ENROLLMENT_OPEN state to close registration'
      ));
    }

    term.state = 'ENROLLMENT_CLOSED';
    term.revision += 1;

    // Transition all OPEN courses to IN_PROGRESS
    Array.from(storage.courses.values())
      .filter(course => course.term_id === req.params.termId && course.state === 'OPEN')
      .forEach(course => {
        course.state = 'IN_PROGRESS';
        course.updated_at = new Date().toISOString();
        course.revision += 1;
      });

    res.json(createSuccessResponse(term));
  } catch (error) {
    res.status(500).json(createErrorResponse('ERR_INTERNAL_SERVER_ERROR', 'Internal server error'));
  }
});

// PATCH /terms/{termId}:conclude - Conclude a term
app.patch('/terms/:termId([0-9a-fA-F\-]{36})\\:conclude', (req, res) => {
  try {
    if (req.auth.role !== 'REGISTRAR') {
      return res.status(403).json(createErrorResponse(
        ERROR_CODES.ERR_UNAUTHORIZED_ROLE,
        'Only registrars can conclude terms'
      ));
    }

    if (!isValidUUID(req.params.termId)) {
      return res.status(400).json(createErrorResponse(
        ERROR_CODES.ERR_INVALID_ID_FORMAT,
        'Invalid term ID format'
      ));
    }

    const term = storage.terms.get(req.params.termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        ERROR_CODES.ERR_TERM_NOT_FOUND,
        'Term not found'
      ));
    }

    if (req.body.revision === undefined) {
      return res.status(400).json(createErrorResponse(
        ERROR_CODES.ERR_MISSING_REQUIRED_FIELD,
        "Required field 'revision' is missing"
      ));
    }

    if (req.body.revision !== term.revision) {
      return res.status(409).json(createErrorResponse(
        ERROR_CODES.ERR_REV_CONFLICT,
        'Term revision conflict'
      ));
    }

    if (term.state !== 'ENROLLMENT_CLOSED') {
      return res.status(409).json(createErrorResponse(
        ERROR_CODES.ERR_TERM_NOT_ACTIVE,
        'Term must be in ENROLLMENT_CLOSED state to conclude'
      ));
    }

    term.state = 'CONCLUDED';
    term.revision += 1;

    // Complete all IN_PROGRESS courses and finalize enrollments
    Array.from(storage.courses.values())
      .filter(course => course.term_id === req.params.termId && course.state === 'IN_PROGRESS')
      .forEach(course => {
        course.state = 'COMPLETED';
        course.updated_at = new Date().toISOString();
        course.revision += 1;

        // Finalize enrollments for this course
        Array.from(storage.enrollments.values())
          .filter(enrollment => enrollment.course_id === course.id)
          .forEach(enrollment => {
            if (enrollment.state === 'ENROLLED') {
              enrollment.state = 'COMPLETED';
            } else if (enrollment.state === 'WAITLISTED') {
              enrollment.state = 'DROPPED';
            }
            enrollment.revision += 1;
          });
      });

    res.json(createSuccessResponse(term));
  } catch (error) {
    res.status(500).json(createErrorResponse('ERR_INTERNAL_SERVER_ERROR', 'Internal server error'));
  }
});

// Course Management Endpoints

// POST /terms/{termId}/courses - Create a new course
app.post('/terms/:termId/courses', (req, res) => {
  try {
    if (!['PROFESSOR', 'REGISTRAR'].includes(req.auth.role)) {
      return res.status(403).json(createErrorResponse(
        ERROR_CODES.ERR_UNAUTHORIZED_ROLE,
        'Only professors and registrars can create courses'
      ));
    }

    if (!isValidUUID(req.params.termId)) {
      return res.status(400).json(createErrorResponse(
        ERROR_CODES.ERR_INVALID_ID_FORMAT,
        'Invalid term ID format'
      ));
    }

    const term = storage.terms.get(req.params.termId);
    if (!term || term.state === 'CONCLUDED') {
      return res.status(404).json(createErrorResponse(
        ERROR_CODES.ERR_TERM_NOT_FOUND,
        'Term not found or concluded'
      ));
    }

    const allowedFields = ['code', 'title', 'description', 'credits', 'capacity', 'professor_id', 'delivery_mode', 'location', 'online_link'];
    validateUnknownFields(req.body, allowedFields);
    validateRequiredFields(req.body, ['code', 'title', 'credits', 'capacity']);

    if (!validateCourseCode(req.body.code)) {
      return res.status(400).json(createErrorResponse(
        ERROR_CODES.ERR_INVALID_COURSE_CODE,
        'Course code must be 2-4 uppercase letters followed by 3 digits'
      ));
    }

    // Check course code uniqueness within term
    const existingCourse = Array.from(storage.courses.values())
      .find(course => course.term_id === req.params.termId && course.code === req.body.code);
    if (existingCourse) {
      return res.status(409).json(createErrorResponse(
        ERROR_CODES.ERR_COURSE_CODE_NOT_UNIQUE,
        'Course code must be unique within the term'
      ));
    }

    if (req.body.title.length === 0 || req.body.title.length > 100) {
      return res.status(400).json(createErrorResponse(
        ERROR_CODES.ERR_INVALID_FIELD_LENGTH,
        'Course title must be between 1 and 100 characters'
      ));
    }

    if (req.body.description && req.body.description.length > 1000) {
      return res.status(400).json(createErrorResponse(
        ERROR_CODES.ERR_INVALID_FIELD_LENGTH,
        'Course description must not exceed 1000 characters'
      ));
    }

    if (req.body.credits < 1 || req.body.credits > 5) {
      return res.status(400).json(createErrorResponse(
        ERROR_CODES.ERR_INVALID_CREDITS,
        'Credits must be between 1 and 5'
      ));
    }

    if (req.body.capacity < 1 || req.body.capacity > 500) {
      return res.status(400).json(createErrorResponse(
        ERROR_CODES.ERR_INVALID_CAPACITY,
        'Capacity must be between 1 and 500'
      ));
    }

    let professorId;
    if (req.auth.role === 'PROFESSOR') {
      if (req.body.professor_id && req.body.professor_id !== req.auth.id) {
        return res.status(400).json(createErrorResponse(
          ERROR_CODES.ERR_FIELD_CONFLICT,
          'Professors can only create courses for themselves'
        ));
      }
      professorId = req.auth.id;
    } else if (req.auth.role === 'REGISTRAR') {
      if (!req.body.professor_id) {
        return res.status(400).json(createErrorResponse(
          ERROR_CODES.ERR_MISSING_REQUIRED_FIELD,
          'Registrar must specify professor_id'
        ));
      }
      professorId = req.body.professor_id;

      // Validate that the provided professor_id belongs to an existing PROFESSOR user
      const targetUser = storage.users.get(professorId);
      if (!targetUser || targetUser.role !== 'PROFESSOR') {
        return res.status(422).json(createErrorResponse(
          ERROR_CODES.ERR_INVALID_INSTRUCTOR,
          'Provided professor_id does not correspond to a valid professor'
        ));
      }
    }

    // Check professor course limit
    const professorCourseCount = getProfessorCourseCount(req.params.termId, professorId);
    if (professorCourseCount >= MAX_COURSES_PER_PROF) {
      return res.status(409).json(createErrorResponse(
        ERROR_CODES.ERR_MAX_COURSES_REACHED,
        'Professor has reached maximum course limit'
      ));
    }

    // Validate delivery mode and conditional fields
    if (req.body.delivery_mode) {
      if (req.body.delivery_mode === 'IN_PERSON' && !req.body.location) {
        return res.status(400).json(createErrorResponse(
          ERROR_CODES.ERR_CONDITIONAL_FIELD_REQUIRED,
          'Location is required for in-person courses'
        ));
      }
      if (req.body.delivery_mode === 'ONLINE' && !req.body.online_link) {
        return res.status(400).json(createErrorResponse(
          ERROR_CODES.ERR_CONDITIONAL_FIELD_REQUIRED,
          'Online link is required for online courses'
        ));
      }
    }

    if (req.body.location && !['IN_PERSON', 'HYBRID'].includes(req.body.delivery_mode)) {
      return res.status(400).json(createErrorResponse(
        ERROR_CODES.ERR_FIELD_CONFLICT,
        'Location can only be provided for in-person or hybrid courses'
      ));
    }

    if (req.body.online_link && !['ONLINE', 'HYBRID'].includes(req.body.delivery_mode)) {
      return res.status(400).json(createErrorResponse(
        ERROR_CODES.ERR_FIELD_CONFLICT,
        'Online link can only be provided for online or hybrid courses'
      ));
    }

    const courseId = uuidv4();
    const course = {
      id: courseId,
      term_id: req.params.termId,
      code: req.body.code,
      title: req.body.title,
      description: req.body.description || '',
      credits: req.body.credits,
      capacity: req.body.capacity,
      professor_id: professorId,
      state: 'DRAFT',
      enrolled_count: 0,
      waitlist_count: 0,
      available_seats: 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      revision: 0
    };

    if (req.body.delivery_mode) {
      course.delivery_mode = req.body.delivery_mode;
      if (req.body.location) course.location = req.body.location;
      if (req.body.online_link) course.online_link = req.body.online_link;
    }

    storage.courses.set(courseId, course);

    res.status(201).json(createSuccessResponse(course));
  } catch (error) {
    if (error.status) {
      return res.status(error.status).json(createErrorResponse(error.errorId, error.message));
    }
    res.status(500).json(createErrorResponse('ERR_INTERNAL_SERVER_ERROR', 'Internal server error'));
  }
});

// GET /terms/{termId}/courses - List courses in a term
app.get('/terms/:termId/courses', (req, res) => {
  try {
    if (!isValidUUID(req.params.termId)) {
      return res.status(400).json(createErrorResponse(
        ERROR_CODES.ERR_INVALID_ID_FORMAT,
        'Invalid term ID format'
      ));
    }

    const term = storage.terms.get(req.params.termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        ERROR_CODES.ERR_TERM_NOT_FOUND,
        'Term not found'
      ));
    }

    let courses = Array.from(storage.courses.values())
      .filter(course => course.term_id === req.params.termId);

    // Apply role-based filtering
    if (req.auth.role === 'STUDENT') {
      // Students only see published courses
      courses = courses.filter(course => course.state !== 'DRAFT' && course.state !== 'CANCELLED');
    } else if (req.auth.role === 'PROFESSOR') {
      // Professors see their own courses regardless of state, plus published courses from others
      courses = courses.filter(course => 
        course.professor_id === req.auth.id || 
        (course.state !== 'DRAFT' && course.state !== 'CANCELLED')
      );
    }
    // Registrars see all courses

    // Apply filters from query parameters
    if (req.query.state) {
      courses = courses.filter(course => course.state === req.query.state);
    }
    if (req.query.professor_id) {
      courses = courses.filter(course => course.professor_id === req.query.professor_id);
    }

    // Apply pagination
    const limit = parseInt(req.query.limit) || 50;
    const offset = parseInt(req.query.offset) || 0;
    const paginatedCourses = courses.slice(offset, offset + limit);

    // Apply role-based field filtering
    const filteredCourses = paginatedCourses.map(course => {
      const filteredCourse = { ...course };
      
      // Update derived fields
      filteredCourse.enrolled_count = getEnrolledCount(course.id);
      filteredCourse.waitlist_count = getWaitlistCount(course.id);
      filteredCourse.available_seats = getAvailableSeats(req.params.termId, course.id);

      if (req.auth.role === 'STUDENT') {
        // Students don't see count details
        delete filteredCourse.enrolled_count;
        delete filteredCourse.waitlist_count;
      } else if (req.auth.role === 'PROFESSOR' && course.professor_id !== req.auth.id) {
        // Professors don't see detailed counts for courses they don't teach
        delete filteredCourse.enrolled_count;
        delete filteredCourse.waitlist_count;
      }
      
      return filteredCourse;
    });

    res.json(createSuccessResponse(filteredCourses, 'array'));
  } catch (error) {
    res.status(500).json(createErrorResponse('ERR_INTERNAL_SERVER_ERROR', 'Internal server error'));
  }
});

// GET /terms/{termId}/courses/{courseId} - Get course details
app.get('/terms/:termId/courses/:courseId', (req, res) => {
  try {
    if (!isValidUUID(req.params.termId) || !isValidUUID(req.params.courseId)) {
      return res.status(400).json(createErrorResponse(
        ERROR_CODES.ERR_INVALID_ID_FORMAT,
        'Invalid ID format'
      ));
    }

    const course = storage.courses.get(req.params.courseId);
    if (!course || course.term_id !== req.params.termId) {
      return res.status(404).json(createErrorResponse(
        ERROR_CODES.ERR_COURSE_NOT_FOUND,
        'Course not found'
      ));
    }

    // Check visibility based on role
    if (req.auth.role === 'STUDENT') {
      if (course.state === 'DRAFT' || course.state === 'CANCELLED') {
        // Check if student is enrolled/waitlisted
        const studentEnrollment = Array.from(storage.enrollments.values())
          .find(e => e.course_id === req.params.courseId && e.student_id === req.auth.id);
        
        if (!studentEnrollment) {
          return res.status(404).json(createErrorResponse(
            ERROR_CODES.ERR_COURSE_NOT_FOUND,
            'Course not found'
          ));
        }
      }
    } else if (req.auth.role === 'PROFESSOR') {
      if (course.professor_id !== req.auth.id && (course.state === 'DRAFT' || course.state === 'CANCELLED')) {
        return res.status(404).json(createErrorResponse(
          ERROR_CODES.ERR_COURSE_NOT_FOUND,
          'Course not found'
        ));
      }
    }

    const responseCourse = { ...course };
    
    // Update derived fields
    responseCourse.enrolled_count = getEnrolledCount(course.id);
    responseCourse.waitlist_count = getWaitlistCount(course.id);
    responseCourse.available_seats = getAvailableSeats(req.params.termId, course.id);

    // Add role-specific fields
    if (req.auth.role === 'STUDENT') {
      const studentEnrollment = Array.from(storage.enrollments.values())
        .find(e => e.course_id === req.params.courseId && e.student_id === req.auth.id);
      
      responseCourse.is_enrolled = studentEnrollment?.state === 'ENROLLED' || false;
      responseCourse.is_waitlisted = studentEnrollment?.state === 'WAITLISTED' || false;
      
      // Don't show count details to students
      delete responseCourse.enrolled_count;
      delete responseCourse.waitlist_count;
    } else if (req.auth.role === 'PROFESSOR' && course.professor_id === req.auth.id) {
      // Include enrollments for the professor's own course
      const enrollments = Array.from(storage.enrollments.values())
        .filter(e => e.course_id === req.params.courseId)
        .map(e => ({
          id: e.id,
          student_id: e.student_id,
          state: e.state,
          grade: e.grade,
          created_at: e.created_at
        }));
      responseCourse.enrollments = enrollments;
    } else if (req.auth.role === 'REGISTRAR') {
      // Include all details for registrar
      const enrollments = Array.from(storage.enrollments.values())
        .filter(e => e.course_id === req.params.courseId)
        .map(e => ({
          id: e.id,
          student_id: e.student_id,
          state: e.state,
          grade: e.grade,
          created_at: e.created_at
        }));
      responseCourse.enrollments = enrollments;
    }

    res.json(createSuccessResponse(responseCourse));
  } catch (error) {
    res.status(500).json(createErrorResponse('ERR_INTERNAL_SERVER_ERROR', 'Internal server error'));
  }
});

// PATCH /terms/{termId}/courses/{courseId}:publish - Publish a course
app.patch('/terms/:termId([0-9a-fA-F\-]{36})/courses/:courseId([0-9a-fA-F\-]{36})\\:publish', (req, res) => {
  try {
    if (!isValidUUID(req.params.termId) || !isValidUUID(req.params.courseId)) {
      return res.status(400).json(createErrorResponse(
        ERROR_CODES.ERR_INVALID_ID_FORMAT,
        'Invalid ID format'
      ));
    }

    const course = storage.courses.get(req.params.courseId);
    if (!course || course.term_id !== req.params.termId) {
      return res.status(404).json(createErrorResponse(
        ERROR_CODES.ERR_COURSE_NOT_FOUND,
        'Course not found'
      ));
    }

    // First, ensure the caller has a role permitted to attempt publishing
    if (!['PROFESSOR', 'REGISTRAR'].includes(req.auth.role)) {
      return res.status(403).json(createErrorResponse(
        ERROR_CODES.ERR_UNAUTHORIZED_ROLE,
        'Caller role is not authorized to publish courses'
      ));
    }

    // If caller is a professor, ensure they are the instructor of this course
    if (req.auth.role === 'PROFESSOR' && course.professor_id !== req.auth.id) {
      return res.status(403).json(createErrorResponse(
        ERROR_CODES.ERR_NOT_INSTRUCTOR,
        'Only the course instructor can publish this course'
      ));
    }

    if (course.state !== 'DRAFT') {
      return res.status(409).json(createErrorResponse(
        ERROR_CODES.ERR_COURSE_WRONG_STATE,
        'Course must be in DRAFT state to publish'
      ));
    }

    const term = storage.terms.get(req.params.termId);
    if (term.state !== 'ENROLLMENT_OPEN') {
      return res.status(409).json(createErrorResponse(
        ERROR_CODES.ERR_TERM_NOT_ACTIVE,
        'Term must be open for enrollment to publish courses'
      ));
    }

    if (req.body.revision !== undefined && req.body.revision !== course.revision) {
      return res.status(409).json(createErrorResponse(
        ERROR_CODES.ERR_REV_CONFLICT,
        'Course revision conflict'
      ));
    }

    course.state = 'OPEN';
    course.updated_at = new Date().toISOString();
    course.revision += 1;
    course.available_seats = course.capacity;

    // Initialize seat ledger
    initCourseSeatLedger(req.params.termId, req.params.courseId, course.capacity);

    res.json(createSuccessResponse(course));
  } catch (error) {
    res.status(500).json(createErrorResponse('ERR_INTERNAL_SERVER_ERROR', 'Internal server error'));
  }
});

// PATCH /terms/{termId}/courses/{courseId}:cancel - Cancel a course
app.patch('/terms/:termId([0-9a-fA-F\-]{36})/courses/:courseId([0-9a-fA-F\-]{36})\\:cancel', (req, res) => {
  try {
    if (!isValidUUID(req.params.termId) || !isValidUUID(req.params.courseId)) {
      return res.status(400).json(createErrorResponse(
        ERROR_CODES.ERR_INVALID_ID_FORMAT,
        'Invalid ID format'
      ));
    }

    const course = storage.courses.get(req.params.courseId);
    if (!course || course.term_id !== req.params.termId) {
      return res.status(404).json(createErrorResponse(
        ERROR_CODES.ERR_COURSE_NOT_FOUND,
        'Course not found'
      ));
    }

    // Only Registrar or the course's instructor can cancel
    if (!['PROFESSOR', 'REGISTRAR'].includes(req.auth.role)) {
      return res.status(403).json(createErrorResponse(
        ERROR_CODES.ERR_UNAUTHORIZED_ROLE,
        'Caller role is not authorized to cancel courses'
      ));
    }

    if (req.auth.role === 'PROFESSOR' && course.professor_id !== req.auth.id) {
      return res.status(403).json(createErrorResponse(
        ERROR_CODES.ERR_NOT_INSTRUCTOR,
        'Only the course instructor can cancel this course'
      ));
    }

    if (course.state === 'COMPLETED' || course.state === 'CANCELLED') {
      return res.status(409).json(createErrorResponse(
        ERROR_CODES.ERR_COURSE_WRONG_STATE,
        'Cannot cancel a completed or already cancelled course'
      ));
    }

    if (req.body.revision !== undefined && req.body.revision !== course.revision) {
      return res.status(409).json(createErrorResponse(
        ERROR_CODES.ERR_REV_CONFLICT,
        'Course revision conflict'
      ));
    }

    course.state = 'CANCELLED';
    course.updated_at = new Date().toISOString();
    course.revision += 1;

    // Drop all enrollments and handle refunds
    Array.from(storage.enrollments.values())
      .filter(enrollment => enrollment.course_id === req.params.courseId && 
               ['ENROLLED', 'WAITLISTED'].includes(enrollment.state))
      .forEach(enrollment => {
        const wasEnrolled = enrollment.state === 'ENROLLED';
        enrollment.state = 'DROPPED';
        enrollment.dropped_by_student = req.auth.role === 'STUDENT';
        enrollment.revision += 1;

        if (wasEnrolled) {
          // Always attempt to free the seat; if that fails we still want to process the tuition refund.
          try {
            creditCourseSeatLedger(req.params.termId, req.params.courseId, 1);
          } catch (error) {
            // Seat ledger error is non-fatal for the refund logic.
          }

          // Tuition refund – MUST occur even if seat-ledger adjustment failed.
          safeDebitStudentTuitionLedger(
            req.params.termId,
            enrollment.student_id,
            course.credits * COST_PER_CREDIT
          );
        }
      });

    res.json(createSuccessResponse(course));
  } catch (error) {
    res.status(500).json(createErrorResponse('ERR_INTERNAL_SERVER_ERROR', 'Internal server error'));
  }
});

// Enrollment & Waitlist Endpoints

// POST /terms/{termId}/courses/{courseId}/enrollments - Create enrollment
app.post('/terms/:termId/courses/:courseId/enrollments', (req, res) => {
  try {
    // Only "student_id" field is valid in request body for this endpoint
    validateUnknownFields(req.body, ['student_id']);
    if (!['STUDENT', 'REGISTRAR'].includes(req.auth.role)) {
      return res.status(403).json(createErrorResponse(
        ERROR_CODES.ERR_UNAUTHORIZED_ROLE,
        'Only students and registrars can create enrollments'
      ));
    }

    if (!isValidUUID(req.params.termId) || !isValidUUID(req.params.courseId)) {
      return res.status(400).json(createErrorResponse(
        ERROR_CODES.ERR_INVALID_ID_FORMAT,
        'Invalid ID format'
      ));
    }

    const term = storage.terms.get(req.params.termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        ERROR_CODES.ERR_TERM_NOT_FOUND,
        'Term not found'
      ));
    }

    // Validation precedence: Term state gating BEFORE course-specific checks
    if (term.state !== 'ENROLLMENT_OPEN') {
      return res.status(409).json(createErrorResponse(
        ERROR_CODES.ERR_REGISTRATION_CLOSED,
        'Registration is closed'
      ));
    }

    const course = storage.courses.get(req.params.courseId);
    if (!course || course.term_id !== req.params.termId) {
      return res.status(404).json(createErrorResponse(
        ERROR_CODES.ERR_COURSE_NOT_FOUND,
        'Course not found'
      ));
    }

    if (course.state !== 'OPEN') {
      return res.status(409).json(createErrorResponse(
        ERROR_CODES.ERR_COURSE_WRONG_STATE,
        'Course must be open for enrollment'
      ));
    }

    let targetStudentId;
    if (req.auth.role === 'STUDENT') {
      if (req.body.student_id && req.body.student_id !== req.auth.id) {
        return res.status(403).json(createErrorResponse(
          ERROR_CODES.ERR_FORBIDDEN,
          'Students can only enroll themselves'
        ));
      }
      targetStudentId = req.auth.id;
    } else if (req.auth.role === 'REGISTRAR') {
      if (!req.body.student_id) {
        return res.status(400).json(createErrorResponse(
          ERROR_CODES.ERR_MISSING_REQUIRED_FIELD,
          'Registrar must specify student_id'
        ));
      }
      targetStudentId = req.body.student_id;

      // Ensure the referenced student exists and is actually a STUDENT
      const targetUser = storage.users.get(targetStudentId);
      if (!targetUser || targetUser.role !== 'STUDENT') {
        return res.status(404).json(createErrorResponse(
          ERROR_CODES.ERR_STUDENT_NOT_FOUND,
          'Student not found'
        ));
      }
    }

    // Check if student is already enrolled or waitlisted
    const existingEnrollment = Array.from(storage.enrollments.values())
      .find(e => e.course_id === req.params.courseId && e.student_id === targetStudentId && 
                 ['ENROLLED', 'WAITLISTED'].includes(e.state));
    
    if (existingEnrollment) {
      return res.status(409).json(createErrorResponse(
        ERROR_CODES.ERR_ALREADY_ENROLLED,
        'Student is already enrolled or waitlisted'
      ));
    }

    // Check credit limit for students (not for registrar override)
    if (req.auth.role === 'STUDENT') {
      const currentCredits = getStudentEnrolledCredits(req.params.termId, targetStudentId);
      if (currentCredits + course.credits > MAX_CREDITS_PER_TERM) {
        return res.status(409).json(createErrorResponse(
          ERROR_CODES.ERR_CREDIT_LIMIT_EXCEEDED,
          'Enrollment would exceed credit limit'
        ));
      }
    }

    // Check seat availability
    const availableSeats = getAvailableSeats(req.params.termId, req.params.courseId);
    const isEnrolled = availableSeats > 0;

    const enrollmentId = uuidv4();
    const enrollment = {
      id: enrollmentId,
      term_id: req.params.termId,
      course_id: req.params.courseId,
      student_id: targetStudentId,
      state: isEnrolled ? 'ENROLLED' : 'WAITLISTED',
      created_at: new Date().toISOString(),
      revision: 0
    };

    storage.enrollments.set(enrollmentId, enrollment);

    // Update ledgers if enrolled
    if (isEnrolled) {
      try {
        debitCourseSeatLedger(req.params.termId, req.params.courseId, 1);
        creditStudentTuitionLedger(req.params.termId, targetStudentId, course.credits * COST_PER_CREDIT);
      } catch (error) {
        // Rollback enrollment if ledger operation fails
        storage.enrollments.delete(enrollmentId);
        return res.status(500).json(createErrorResponse('ERR_INTERNAL_SERVER_ERROR', 'Ledger operation failed'));
      }
    }

    res.status(201).json(createSuccessResponse(enrollment));
  } catch (error) {
    if (error.status) {
      return res.status(error.status).json(createErrorResponse(error.errorId, error.message));
    }
    res.status(500).json(createErrorResponse('ERR_INTERNAL_SERVER_ERROR', 'Internal server error'));
  }
});

// GET /terms/{termId}/courses/{courseId}/enrollments - List course enrollments
app.get('/terms/:termId/courses/:courseId/enrollments', (req, res) => {
  try {
    if (!isValidUUID(req.params.termId) || !isValidUUID(req.params.courseId)) {
      return res.status(400).json(createErrorResponse(
        ERROR_CODES.ERR_INVALID_ID_FORMAT,
        'Invalid ID format'
      ));
    }

    const course = storage.courses.get(req.params.courseId);
    if (!course || course.term_id !== req.params.termId) {
      return res.status(404).json(createErrorResponse(
        ERROR_CODES.ERR_COURSE_NOT_FOUND,
        'Course not found'
      ));
    }

    // Only professors (for their course) and registrars can list enrollments
    if (req.auth.role === 'STUDENT') {
      return res.status(403).json(createErrorResponse(
        ERROR_CODES.ERR_UNAUTHORIZED_ROLE,
        'Students cannot list course enrollments'
      ));
    }

    if (req.auth.role === 'PROFESSOR' && course.professor_id !== req.auth.id) {
      return res.status(403).json(createErrorResponse(
        ERROR_CODES.ERR_NOT_INSTRUCTOR,
        'Professors can only list enrollments for their own courses'
      ));
    }

    let enrollments = Array.from(storage.enrollments.values())
      .filter(e => e.course_id === req.params.courseId);

    // Apply pagination
    const limit = parseInt(req.query.limit) || 50;
    const offset = parseInt(req.query.offset) || 0;
    const paginatedEnrollments = enrollments.slice(offset, offset + limit);

    res.json(createSuccessResponse(paginatedEnrollments, 'array'));
  } catch (error) {
    res.status(500).json(createErrorResponse('ERR_INTERNAL_SERVER_ERROR', 'Internal server error'));
  }
});

// GET /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId} - Get specific enrollment
app.get('/terms/:termId/courses/:courseId/enrollments/:enrollmentId', (req, res) => {
  try {
    if (!isValidUUID(req.params.termId) || !isValidUUID(req.params.courseId) || !isValidUUID(req.params.enrollmentId)) {
      return res.status(400).json(createErrorResponse(
        ERROR_CODES.ERR_INVALID_ID_FORMAT,
        'Invalid ID format'
      ));
    }

    const enrollment = storage.enrollments.get(req.params.enrollmentId);
    if (!enrollment || enrollment.course_id !== req.params.courseId || enrollment.term_id !== req.params.termId) {
      return res.status(404).json(createErrorResponse(
        ERROR_CODES.ERR_ENROLLMENT_NOT_FOUND,
        'Enrollment not found'
      ));
    }

    const course = storage.courses.get(req.params.courseId);
    
    // Check permissions
    if (req.auth.role === 'STUDENT' && enrollment.student_id !== req.auth.id) {
      return res.status(403).json(createErrorResponse(
        ERROR_CODES.ERR_FORBIDDEN,
        'Students can only view their own enrollments'
      ));
    }

    if (req.auth.role === 'PROFESSOR' && course.professor_id !== req.auth.id) {
      return res.status(403).json(createErrorResponse(
        ERROR_CODES.ERR_NOT_INSTRUCTOR,
        'Professors can only view enrollments for their own courses'
      ));
    }

    res.json(createSuccessResponse(enrollment));
  } catch (error) {
    res.status(500).json(createErrorResponse('ERR_INTERNAL_SERVER_ERROR', 'Internal server error'));
  }
});

// PATCH /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}:drop - Drop enrollment
app.patch('/terms/:termId([0-9a-fA-F\-]{36})/courses/:courseId([0-9a-fA-F\-]{36})/enrollments/:enrollmentId([0-9a-fA-F\-]{36})\\:drop', (req, res) => {
  try {
    // Only "revision" is allowed in request body
    validateUnknownFields(req.body, ['revision']);
    if (!isValidUUID(req.params.termId) || !isValidUUID(req.params.courseId) || !isValidUUID(req.params.enrollmentId)) {
      return res.status(400).json(createErrorResponse(
        ERROR_CODES.ERR_INVALID_ID_FORMAT,
        'Invalid ID format'
      ));
    }

    const term = storage.terms.get(req.params.termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        ERROR_CODES.ERR_TERM_NOT_FOUND,
        'Term not found'
      ));
    }

    // Updated term-state gating logic
    if (term.state === 'CONCLUDED') {
      return res.status(409).json(createErrorResponse(
        ERROR_CODES.ERR_TERM_NOT_ACTIVE,
        'Cannot drop after term is concluded'
      ));
    }
    if (term.state === 'ENROLLMENT_CLOSED' && req.auth.role !== 'REGISTRAR') {
      return res.status(409).json(createErrorResponse(
        ERROR_CODES.ERR_REGISTRATION_CLOSED,
        'Registration is closed'
      ));
    }

    const enrollment = storage.enrollments.get(req.params.enrollmentId);
    if (!enrollment || enrollment.course_id !== req.params.courseId || enrollment.term_id !== req.params.termId) {
      return res.status(404).json(createErrorResponse(
        ERROR_CODES.ERR_ENROLLMENT_NOT_FOUND,
        'Enrollment not found'
      ));
    }

    const course = storage.courses.get(req.params.courseId);

    // Check permissions
    let authorized = false;
    if (req.auth.role === 'REGISTRAR') {
      authorized = true;
    } else if (req.auth.role === 'STUDENT' && enrollment.student_id === req.auth.id) {
      authorized = true;
    } else if (req.auth.role === 'PROFESSOR' && course.professor_id === req.auth.id) {
      authorized = true;
    }

    if (!authorized) {
      return res.status(403).json(createErrorResponse(
        ERROR_CODES.ERR_PERMISSION_DENIED,
        'Not authorized to drop this enrollment'
      ));
    }

    if (!['ENROLLED', 'WAITLISTED'].includes(enrollment.state)) {
      return res.status(409).json(createErrorResponse(
        ERROR_CODES.ERR_ENROLLMENT_WRONG_STATE,
        'Enrollment cannot be dropped in current state'
      ));
    }

    // Check drop count limit for students
    if (req.auth.role === 'STUDENT') {
      const currentDropCount = getStudentDropCount(req.params.termId, req.auth.id);
      if (currentDropCount >= MAX_DROP_COUNT_PER_TERM) {
        return res.status(409).json(createErrorResponse(
          ERROR_CODES.ERR_TOO_MANY_DROPS,
          'Student has reached maximum drop limit'
        ));
      }
    }

    if (req.body.revision !== undefined && req.body.revision !== enrollment.revision) {
      return res.status(409).json(createErrorResponse(
        ERROR_CODES.ERR_REV_CONFLICT,
        'Enrollment revision conflict'
      ));
    }

    const wasEnrolled = enrollment.state === 'ENROLLED';
    enrollment.state = 'DROPPED';
    enrollment.dropped_by_student = req.auth.role === 'STUDENT';
    enrollment.revision += 1;

    // Handle ledger updates
    if (wasEnrolled) {
      try {
        // Free up seat and refund tuition
        creditCourseSeatLedger(req.params.termId, req.params.courseId, 1);
        safeDebitStudentTuitionLedger(req.params.termId, enrollment.student_id, course.credits * COST_PER_CREDIT);
        
        // Trigger waitlist promotion
        triggerSeatVacated(req.params.courseId);
      } catch (error) {
        // Handle ledger errors gracefully
      }
    }

    // Apply drop penalty for student's 3rd drop
    if (req.auth.role === 'STUDENT') {
      const newDropCount = getStudentDropCount(req.params.termId, req.auth.id);
      if (newDropCount === MAX_DROP_COUNT_PER_TERM) {
        try {
          creditStudentTuitionLedger(req.params.termId, req.auth.id, DROP_PENALTY_FEE);
        } catch (error) {
          // Handle ledger error
        }
      }
    }

    res.json(createSuccessResponse(enrollment));
  } catch (error) {
    // Propagate well-formed business errors (those that include a `status` field) instead of
    // collapsing them into a generic 500. This aligns the endpoint with the global error
    // handling pattern used across the rest of the API (e.g. course creation, term actions),
    // ensuring callers receive the correct error_id such as ERR_UNKNOWN_FIELD or
    // ERR_INVALID_ID_FORMAT.
    if (error && error.status) {
      return res.status(error.status).json(createErrorResponse(error.errorId, error.message));
    }
    res.status(500).json(createErrorResponse('ERR_INTERNAL_SERVER_ERROR', 'Internal server error'));
  }
});

// Payment Endpoints

// POST /terms/{termId}/students/{studentId}:pay - Make tuition payment
app.post('/terms/:termId([0-9a-fA-F\-]{36})/students/:studentId([0-9a-fA-F\-]{36})\\:pay', (req, res) => {
  try {
    // Only "amount" is permitted in the payment body
    validateUnknownFields(req.body, ['amount']);
    if (!['STUDENT', 'REGISTRAR'].includes(req.auth.role)) {
      return res.status(403).json(createErrorResponse(
        ERROR_CODES.ERR_UNAUTHORIZED_ROLE,
        'Only students and registrars can make payments'
      ));
    }

    if (!isValidUUID(req.params.termId) || !isValidUUID(req.params.studentId)) {
      return res.status(400).json(createErrorResponse(
        ERROR_CODES.ERR_INVALID_ID_FORMAT,
        'Invalid ID format'
      ));
    }

    if (req.auth.role === 'STUDENT' && req.auth.id !== req.params.studentId) {
      return res.status(403).json(createErrorResponse(
        ERROR_CODES.ERR_FORBIDDEN,
        'Students can only pay their own balance'
      ));
    }

    const term = storage.terms.get(req.params.termId);
    if (!term) {
      return res.status(404).json(createErrorResponse(
        ERROR_CODES.ERR_TERM_NOT_FOUND,
        'Term not found'
      ));
    }

    validateRequiredFields(req.body, ['amount']);
    
    if (typeof req.body.amount !== 'number' || req.body.amount <= 0) {
      return res.status(422).json(createErrorResponse(
        ERROR_CODES.ERR_INVALID_PAYMENT_AMOUNT,
        'Payment amount must be a positive number'
      ));
    }

    // Ensure the referenced student exists and is actually a STUDENT
    const targetUser = storage.users.get(req.params.studentId);
    if (!targetUser || targetUser.role !== 'STUDENT') {
      return res.status(404).json(createErrorResponse(
        ERROR_CODES.ERR_STUDENT_NOT_FOUND,
        'Student not found'
      ));
    }

    // Initialize ledger if needed
    initStudentTuitionLedger(req.params.termId, req.params.studentId);
    
    const ledgerKey = `${req.params.termId}-${req.params.studentId}`;
    const ledger = storage.studentTuitionLedgers.get(ledgerKey);
    
    if (req.body.amount > ledger.balance_cents) {
      return res.status(422).json(createErrorResponse(
        ERROR_CODES.ERR_OVERPAY_NOT_ALLOWED,
        'Payment amount exceeds outstanding balance'
      ));
    }

    try {
      debitStudentTuitionLedger(req.params.termId, req.params.studentId, req.body.amount);
      
      const updatedLedger = storage.studentTuitionLedgers.get(ledgerKey);
      
      res.json(createSuccessResponse({
        student_id: req.params.studentId,
        term_id: req.params.termId,
        new_balance: updatedLedger.balance_cents
      }));
    } catch (error) {
      return res.status(500).json(createErrorResponse(
        ERROR_CODES.ERR_LEDGER_INVALID_OP,
        'Ledger operation failed'
      ));
    }
  } catch (error) {
    if (error.status) {
      return res.status(error.status).json(createErrorResponse(error.errorId, error.message));
    }
    res.status(500).json(createErrorResponse('ERR_INTERNAL_SERVER_ERROR', 'Internal server error'));
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json(createErrorResponse('ERR_INTERNAL_SERVER_ERROR', 'Internal server error'));
});

// 404 handler
app.use((req, res) => {
  res.status(404).json(createErrorResponse('ERR_NOT_FOUND', 'Endpoint not found'));
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

module.exports = app;